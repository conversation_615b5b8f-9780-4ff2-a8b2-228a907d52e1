import { drizzle } from 'drizzle-orm/postgres-js'
import postgres from 'postgres'
import * as schema from './schema'

const connectionString = process.env.DATABASE_URL

if (!connectionString) {
  throw new Error('DATABASE_URL environment variable is required')
}

if (connectionString.includes('[YOUR-PASSWORD]')) {
  throw new Error('Please replace [YOUR-PASSWORD] in DATABASE_URL with your actual Supabase database password')
}

// Disable prefetch as it is not supported for "Transaction" pool mode
const client = postgres(connectionString, {
  prepare: false,
  max: 1, // Limit connections for development
  idle_timeout: 20,
  max_lifetime: 60 * 30
})

export const db = drizzle(client, { schema })
