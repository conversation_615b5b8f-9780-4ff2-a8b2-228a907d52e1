import { db } from './index.js'
import { cats } from './schema.js'

export const seedData = [
  {
    name: 'Whiskers',
    breed: 'Persian',
    age: 3,
    color: 'White',
    description: 'A fluffy and friendly Persian cat who loves to play with yarn and enjoys long naps in sunny spots.'
  },
  {
    name: '<PERSON>',
    breed: 'Maine Coon',
    age: 5,
    color: 'Black',
    description: 'A large and majestic Maine Coon with a gentle personality and impressive hunting skills.'
  },
  {
    name: '<PERSON>',
    breed: 'Siamese',
    age: 2,
    color: 'Cream',
    description: 'An elegant Siamese cat with beautiful blue eyes and a very vocal, social personality.'
  },
  {
    name: '<PERSON>',
    breed: 'British Shorthair',
    age: 4,
    color: 'Orange',
    description: 'A calm and affectionate British Shorthair with a beautiful orange coat and round face.'
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    breed: '<PERSON><PERSON><PERSON><PERSON>',
    age: 6,
    color: 'Bi-color',
    description: 'A docile Ragdoll cat with striking blue eyes who goes limp when picked up, true to the breed name.'
  },
  {
    name: '<PERSON>',
    breed: '<PERSON>',
    age: 3,
    color: '<PERSON>bby',
    description: 'An energetic Bengal cat with wild-looking markings and a playful, adventurous spirit.'
  },
  {
    name: '<PERSON><PERSON>',
    breed: 'Turkish Angora',
    age: 4,
    color: 'White',
    description: 'A graceful Turkish Angora with silky white fur and heterochromia (one blue, one amber eye).'
  },
  {
    name: 'Smokey',
    breed: 'Russian Blue',
    age: 5,
    color: 'Gray',
    description: 'A reserved Russian Blue with a plush silver-blue coat and bright green eyes.'
  },
  {
    name: 'Patches',
    breed: 'Calico',
    age: 7,
    color: 'Calico',
    description: 'A beautiful calico cat with patches of orange, black, and white fur and a sassy attitude.'
  },
  {
    name: 'Felix',
    breed: 'Tuxedo',
    age: 2,
    color: 'Tuxedo',
    description: 'A dapper tuxedo cat with perfect black and white markings that make him look like he\'s wearing a suit.'
  },
  {
    name: 'Cleo',
    breed: 'Egyptian Mau',
    age: 4,
    color: 'Silver',
    description: 'A spotted Egyptian Mau with natural leopard-like markings and incredible speed.'
  },
  {
    name: 'Oreo',
    breed: 'Mixed Breed',
    age: 3,
    color: 'Black',
    description: 'A sweet mixed breed rescue cat with black and white markings reminiscent of the famous cookie.'
  }
]

export async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...')
    
    // Clear existing data
    await db.delete(cats)
    console.log('🗑️  Cleared existing cat data')
    
    // Insert seed data
    const insertedCats = await db.insert(cats).values(seedData).returning()
    
    console.log(`✅ Successfully seeded ${insertedCats.length} cats into the database`)
    
    return insertedCats
  } catch (error) {
    console.error('❌ Error seeding database:', error)
    throw error
  }
}
