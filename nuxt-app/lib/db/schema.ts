import { pgTable, uuid, varchar, integer, text, timestamp } from 'drizzle-orm/pg-core'
import { sql } from 'drizzle-orm'

export const cats = pgTable('cats', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  name: varchar('name', { length: 100 }).notNull(),
  breed: varchar('breed', { length: 100 }).notNull(),
  age: integer('age').notNull(),
  color: varchar('color', { length: 50 }).notNull(),
  description: text('description'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
})

export type Cat = typeof cats.$inferSelect
export type NewCat = typeof cats.$inferInsert
