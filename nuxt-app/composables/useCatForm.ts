import { z } from 'zod'
import { createCatSchema, updateCatSchema } from '../app/types/cat'
import type { CatFormState, FormErrors, CreateCatInput, UpdateCatInput } from '../app/types/cat'

export const useCatForm = (initialData?: Partial<CatFormState>) => {
  // Form state
  const formState = reactive<CatFormState>({
    name: initialData?.name || '',
    breed: initialData?.breed || '',
    age: initialData?.age || null,
    color: initialData?.color || '',
    description: initialData?.description || ''
  })

  // Loading and error states
  const loading = ref(false)
  const errors = ref<FormErrors>({})

  // Validation functions
  const validateField = (field: keyof CatFormState, value: any): string | null => {
    try {
      const schema = field === 'age' ? z.number().int().min(0).max(30) : z.string().min(1)
      schema.parse(value)
      return null
    } catch (error) {
      if (error instanceof z.ZodError) {
        return error.errors[0]?.message || 'Invalid value'
      }
      return 'Invalid value'
    }
  }

  const validateForm = (isUpdate = false): boolean => {
    const schema = isUpdate ? updateCatSchema : createCatSchema
    const data = {
      name: formState.name,
      breed: formState.breed,
      age: formState.age,
      color: formState.color,
      description: formState.description || undefined
    }

    try {
      schema.parse(data)
      errors.value = {}
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: FormErrors = {}
        error.errors.forEach((err) => {
          const field = err.path[0] as keyof FormErrors
          newErrors[field] = err.message
        })
        errors.value = newErrors
      }
      return false
    }
  }

  // Form data getters
  const getCreateData = (): CreateCatInput => ({
    name: formState.name,
    breed: formState.breed,
    age: formState.age!,
    color: formState.color,
    description: formState.description || undefined
  })

  const getUpdateData = (): UpdateCatInput => {
    const data: UpdateCatInput = {}
    if (formState.name) data.name = formState.name
    if (formState.breed) data.breed = formState.breed
    if (formState.age !== null) data.age = formState.age
    if (formState.color) data.color = formState.color
    if (formState.description) data.description = formState.description
    return data
  }

  // Reset form
  const resetForm = () => {
    formState.name = ''
    formState.breed = ''
    formState.age = null
    formState.color = ''
    formState.description = ''
    errors.value = {}
  }

  // Update form with new data
  const updateForm = (data: Partial<CatFormState>) => {
    Object.assign(formState, data)
  }

  // Check if form has changes
  const hasChanges = (originalData?: Partial<CatFormState>): boolean => {
    if (!originalData) return true
    
    return (
      formState.name !== (originalData.name || '') ||
      formState.breed !== (originalData.breed || '') ||
      formState.age !== (originalData.age || null) ||
      formState.color !== (originalData.color || '') ||
      formState.description !== (originalData.description || '')
    )
  }

  return {
    formState: readonly(formState),
    loading: readonly(loading),
    errors: readonly(errors),
    validateField,
    validateForm,
    getCreateData,
    getUpdateData,
    resetForm,
    updateForm,
    hasChanges,
    setLoading: (value: boolean) => { loading.value = value },
    setError: (field: keyof FormErrors, message: string) => { errors.value[field] = message },
    clearErrors: () => { errors.value = {} }
  }
}
