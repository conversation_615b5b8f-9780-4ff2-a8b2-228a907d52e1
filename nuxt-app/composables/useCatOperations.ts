import { CatApi, handleApiError } from '../app/utils/api'
import type { CreateCatInput, UpdateCatInput, CatResponse } from '../app/types/cat'

export const useCatOperations = () => {
  const toast = useToast()

  // Create cat with error handling
  const createCat = async (catData: CreateCatInput): Promise<CatResponse | null> => {
    try {
      const newCat = await CatApi.createCat(catData)
      
      toast.add({
        title: 'Success',
        description: `${newCat.name} has been added successfully`,
        color: 'green'
      })
      
      return newCat
    } catch (error: any) {
      const errorMessage = handleApiError(error)
      
      toast.add({
        title: 'Error',
        description: errorMessage,
        color: 'red'
      })
      
      return null
    }
  }

  // Update cat with error handling
  const updateCat = async (id: string, catData: UpdateCatInput): Promise<CatResponse | null> => {
    try {
      const updatedCat = await CatApi.updateCat(id, catData)
      
      toast.add({
        title: 'Success',
        description: `${updatedCat.name} has been updated successfully`,
        color: 'green'
      })
      
      return updatedCat
    } catch (error: any) {
      const errorMessage = handleApiError(error)
      
      toast.add({
        title: 'Error',
        description: errorMessage,
        color: 'red'
      })
      
      return null
    }
  }

  // Delete cat with confirmation and error handling
  const deleteCat = async (id: string, catName: string): Promise<boolean> => {
    const confirmed = confirm(`Are you sure you want to delete ${catName}?`)
    if (!confirmed) return false

    try {
      await CatApi.deleteCat(id)
      
      toast.add({
        title: 'Success',
        description: `${catName} has been deleted`,
        color: 'green'
      })
      
      return true
    } catch (error: any) {
      const errorMessage = handleApiError(error)
      
      toast.add({
        title: 'Error',
        description: errorMessage,
        color: 'red'
      })
      
      return false
    }
  }

  // Get cat with error handling
  const getCat = async (id: string): Promise<CatResponse | null> => {
    try {
      return await CatApi.getCatById(id)
    } catch (error: any) {
      const errorMessage = handleApiError(error)
      
      toast.add({
        title: 'Error',
        description: errorMessage,
        color: 'red'
      })
      
      return null
    }
  }

  // Get all cats with error handling
  const getAllCats = async () => {
    try {
      return await CatApi.getAllCats()
    } catch (error: any) {
      const errorMessage = handleApiError(error)
      
      toast.add({
        title: 'Error',
        description: errorMessage,
        color: 'red'
      })
      
      return { cats: [], total: 0 }
    }
  }

  return {
    createCat,
    updateCat,
    deleteCat,
    getCat,
    getAllCats
  }
}
