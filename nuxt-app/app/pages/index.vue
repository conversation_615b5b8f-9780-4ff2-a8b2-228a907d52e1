<template>
  <UContainer class="py-16">
    <div class="text-center max-w-3xl mx-auto">
      <div class="mb-8">
        <UIcon name="i-lucide-cat" class="w-24 h-24 mx-auto text-primary mb-6" />
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
          Cat Management System
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-400 mb-8">
          Keep track of your feline friends with our comprehensive cat management application
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <UCard class="text-center">
          <template #header>
            <UIcon name="i-lucide-plus-circle" class="w-12 h-12 mx-auto text-green-500" />
          </template>
          <h3 class="text-lg font-semibold mb-2">Add Cats</h3>
          <p class="text-gray-600 dark:text-gray-400 text-sm">
            Easily add new cats to your collection with detailed information
          </p>
        </UCard>

        <UCard class="text-center">
          <template #header>
            <UIcon name="i-lucide-list" class="w-12 h-12 mx-auto text-blue-500" />
          </template>
          <h3 class="text-lg font-semibold mb-2">Manage Records</h3>
          <p class="text-gray-600 dark:text-gray-400 text-sm">
            View, edit, and organize all your cat records in one place
          </p>
        </UCard>

        <UCard class="text-center">
          <template #header>
            <UIcon name="i-lucide-search" class="w-12 h-12 mx-auto text-purple-500" />
          </template>
          <h3 class="text-lg font-semibold mb-2">Track Details</h3>
          <p class="text-gray-600 dark:text-gray-400 text-sm">
            Keep detailed records of breed, age, color, and descriptions
          </p>
        </UCard>
      </div>

      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <UButton
          to="/cats"
          icon="i-lucide-cat"
          size="xl"
          color="primary"
        >
          View All Cats
        </UButton>
        <UButton
          to="/cats/new"
          icon="i-lucide-plus"
          size="xl"
          variant="outline"
          color="primary"
        >
          Add New Cat
        </UButton>
      </div>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
// SEO
useSeoMeta({
  title: 'Cat Management System',
  description: 'A comprehensive system for managing your feline friends with detailed records and easy organization'
})
</script>
