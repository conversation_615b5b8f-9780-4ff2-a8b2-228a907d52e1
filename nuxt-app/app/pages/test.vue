<template>
  <UContainer class="py-8">
    <UCard>
      <template #header>
        <h1 class="text-2xl font-bold">Test Page</h1>
      </template>
      
      <div class="space-y-4">
        <p>This is a test page to verify the application is working.</p>
        
        <UButton @click="testApi" :loading="loading">
          Test API
        </UButton>
        
        <div v-if="apiResponse" class="p-4 bg-gray-100 dark:bg-gray-800 rounded">
          <pre>{{ JSON.stringify(apiResponse, null, 2) }}</pre>
        </div>
      </div>
    </UCard>
  </UContainer>
</template>

<script setup lang="ts">
const loading = ref(false)
const apiResponse = ref(null)

const testApi = async () => {
  loading.value = true
  try {
    const response = await $fetch('/api/test')
    apiResponse.value = response
  } catch (error) {
    console.error('API test failed:', error)
    apiResponse.value = { error: 'API test failed' }
  } finally {
    loading.value = false
  }
}
</script>
