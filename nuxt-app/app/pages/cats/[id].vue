<template>
  <UContainer class="py-8">
    <!-- Back <PERSON><PERSON> -->
    <UButton
      to="/cats"
      icon="i-lucide-arrow-left"
      variant="ghost"
      color="gray"
      class="mb-6"
    >
      Back to Cats
    </UButton>

    <!-- Loading State -->
    <div v-if="pending" class="flex justify-center py-12">
      <UIcon name="i-lucide-loader-2" class="w-8 h-8 animate-spin" />
    </div>

    <!-- Error State -->
    <UAlert
      v-else-if="error"
      icon="i-lucide-alert-circle"
      color="red"
      variant="soft"
      title="Error loading cat"
      :description="error.message"
      class="mb-6"
    />

    <!-- Cat Details -->
    <div v-else-if="cat" class="max-w-2xl mx-auto">
      <UCard>
        <template #header>
          <div class="flex justify-between items-start">
            <div>
              <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                {{ cat.name }}
              </h1>
              <p class="text-lg text-gray-600 dark:text-gray-400 mt-1">
                {{ cat.breed }}
              </p>
            </div>
            <UDropdownMenu :items="dropdownItems">
              <UButton
                icon="i-lucide-more-vertical"
                variant="ghost"
                color="gray"
              />
            </UDropdownMenu>
          </div>
        </template>

        <div class="space-y-6">
          <!-- Basic Information -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <UIcon name="i-lucide-calendar" class="w-5 h-5 text-blue-500" />
              <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">Age</p>
                <p class="font-semibold text-gray-900 dark:text-white">
                  {{ cat.age }} {{ cat.age === 1 ? 'year' : 'years' }} old
                </p>
              </div>
            </div>

            <div class="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <UIcon name="i-lucide-palette" class="w-5 h-5 text-green-500" />
              <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">Color</p>
                <p class="font-semibold text-gray-900 dark:text-white">
                  {{ cat.color }}
                </p>
              </div>
            </div>
          </div>

          <!-- Description -->
          <div v-if="cat.description">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Description
            </h3>
            <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
              {{ cat.description }}
            </p>
          </div>

          <!-- Timestamps -->
          <div class="border-t pt-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Record Information
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p class="text-gray-600 dark:text-gray-400">Created</p>
                <p class="font-medium text-gray-900 dark:text-white">
                  {{ formatDateTime(cat.createdAt) }}
                </p>
              </div>
              <div v-if="cat.updatedAt !== cat.createdAt">
                <p class="text-gray-600 dark:text-gray-400">Last Updated</p>
                <p class="font-medium text-gray-900 dark:text-white">
                  {{ formatDateTime(cat.updatedAt) }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex gap-3">
            <UButton
              :to="`/cats/${cat.id}/edit`"
              icon="i-lucide-edit"
              color="primary"
            >
              Edit Cat
            </UButton>
            <UButton
              icon="i-lucide-trash-2"
              color="red"
              variant="outline"
              @click="deleteCat"
            >
              Delete Cat
            </UButton>
          </div>
        </template>
      </UCard>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
import { CatApi } from '../../utils/api'

// Get cat ID from route
const route = useRoute()
const catId = route.params.id as string

// Data fetching
const { data: cat, pending, error } = await useLazyAsyncData(`cat-${catId}`, () => CatApi.getCatById(catId))

// SEO
useSeoMeta({
  title: computed(() => cat.value ? `${cat.value.name} - Cat Details` : 'Cat Details'),
  description: computed(() => cat.value ? `Details for ${cat.value.name}, a ${cat.value.breed}` : 'Cat details')
})

// Dropdown actions
const dropdownItems = [
  [{
    label: 'Edit',
    icon: 'i-lucide-edit',
    click: () => navigateTo(`/cats/${catId}/edit`)
  }],
  [{
    label: 'Delete',
    icon: 'i-lucide-trash-2',
    click: () => deleteCat()
  }]
]

// Delete cat function
const deleteCat = async () => {
  if (!cat.value) return

  const confirmed = confirm(`Are you sure you want to delete ${cat.value.name}?`)
  if (!confirmed) return

  try {
    await CatApi.deleteCat(catId)
    
    const toast = useToast()
    toast.add({
      title: 'Success',
      description: `${cat.value.name} has been deleted`,
      color: 'green'
    })
    
    await navigateTo('/cats')
  } catch (error: any) {
    const toast = useToast()
    toast.add({
      title: 'Error',
      description: error.message || 'Failed to delete cat',
      color: 'red'
    })
  }
}

// Date formatting
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}
</script>
