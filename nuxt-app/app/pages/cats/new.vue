<template>
  <UContainer class="py-8">
    <!-- Back <PERSON> -->
    <UButton
      to="/cats"
      icon="i-lucide-arrow-left"
      variant="ghost"
      color="primary"
      class="mb-6"
    >
      Back to Cats
    </UButton>

    <div class="max-w-2xl mx-auto">
      <UCard>
        <template #header>
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
              Add New Cat
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">
              Fill in the details for your new feline friend
            </p>
          </div>
        </template>

        <ClientOnlyForm>
          <UForm
            :schema="createCatSchema"
            :state="formState"
            @submit="onSubmit"
            class="space-y-6"
          >
          <!-- Name Field -->
          <UFormGroup label="Name" name="name" required>
            <UInput
              v-model="formState.name"
              placeholder="Enter cat's name"
              icon="i-lucide-cat"
            />
          </UFormGroup>

          <!-- Breed Field -->
          <UFormGroup label="Breed" name="breed" required>
            <USelectMenu
              v-model="formState.breed"
              :options="breedOptions"
              placeholder="Select breed"
              searchable
            />
          </UFormGroup>

          <!-- Age Field -->
          <UFormGroup label="Age" name="age" required>
            <UInput
              v-model="formState.age"
              type="number"
              placeholder="Enter age in years"
              min="0"
              max="30"
              icon="i-lucide-calendar"
              @input="(event: Event) => formState.age = (event.target as HTMLInputElement).value ? Number((event.target as HTMLInputElement).value) : undefined"
            />
          </UFormGroup>

          <!-- Color Field -->
          <UFormGroup label="Color" name="color" required>
            <USelectMenu
              v-model="formState.color"
              :options="colorOptions"
              placeholder="Select color"
              searchable
            />
          </UFormGroup>

          <!-- Description Field -->
          <UFormGroup label="Description" name="description">
            <UTextarea
              v-model="formState.description"
              placeholder="Tell us about this cat (optional)"
              :rows="4"
            />
          </UFormGroup>

          <!-- Submit Button -->
          <div class="flex gap-3 pt-4">
            <UButton
              type="submit"
              icon="i-lucide-plus"
              :loading="loading"
              :disabled="loading"
              size="lg"
            >
              Add Cat
            </UButton>
            <UButton
              to="/cats"
              variant="outline"
              color="primary"
              size="lg"
            >
              Cancel
            </UButton>
          </div>
          </UForm>
        </ClientOnlyForm>
      </UCard>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
import { CatApi } from '../../utils/api'
import { createCatSchema, CAT_BREEDS, CAT_COLORS } from '../../types/cat'

// SEO
useSeoMeta({
  title: 'Add New Cat',
  description: 'Add a new cat to your collection'
})

// Form state
const formState = reactive({
  name: '',
  breed: '',
  age: undefined as number | undefined,
  color: '',
  description: ''
})

const loading = ref(false)

// Options for dropdowns
const breedOptions = CAT_BREEDS.map(breed => ({
  label: breed,
  value: breed
}))

const colorOptions = CAT_COLORS.map(color => ({
  label: color,
  value: color
}))

// Form submission
const onSubmit = async () => {
  loading.value = true

  try {
    // Validate that age is provided
    if (formState.age === undefined || formState.age === null) {
      const toast = useToast()
      toast.add({
        title: 'Validation Error',
        description: 'Age is required',
        color: 'error'
      })
      loading.value = false
      return
    }

    const catData = {
      name: formState.name,
      breed: formState.breed,
      age: formState.age,
      color: formState.color,
      description: formState.description || undefined
    }

    const newCat = await CatApi.createCat(catData)
    
    const toast = useToast()
    toast.add({
      title: 'Success',
      description: `${newCat.name} has been added successfully`,
      color: 'success'
    })
    
    await navigateTo(`/cats/${newCat.id}`)
  } catch (error: any) {
    const toast = useToast()
    toast.add({
      title: 'Error',
      description: error.message || 'Failed to add cat',
      color: 'error'
    })
  } finally {
    loading.value = false
  }
}
</script>
