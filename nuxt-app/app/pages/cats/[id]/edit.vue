<template>
  <UContainer class="py-8">
    <!-- Back <PERSON><PERSON> -->
    <UButton
      :to="`/cats/${catId}`"
      icon="i-lucide-arrow-left"
      variant="ghost"
      color="gray"
      class="mb-6"
    >
      Back to Cat Details
    </UButton>

    <!-- Loading State -->
    <div v-if="pending" class="flex justify-center py-12">
      <UIcon name="i-lucide-loader-2" class="w-8 h-8 animate-spin" />
    </div>

    <!-- Error State -->
    <UAlert
      v-else-if="error"
      icon="i-lucide-alert-circle"
      color="red"
      variant="soft"
      title="Error loading cat"
      :description="error.message"
      class="mb-6"
    />

    <!-- Edit Form -->
    <div v-else-if="cat" class="max-w-2xl mx-auto">
      <UCard>
        <template #header>
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
              Edit {{ cat.name }}
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">
              Update your cat's information
            </p>
          </div>
        </template>

        <ClientOnlyForm>
          <UForm
            :schema="updateCatSchema"
            :state="formState"
            @submit="onSubmit"
            class="space-y-6"
          >
          <!-- Name Field -->
          <UFormGroup label="Name" name="name">
            <UInput
              v-model="formState.name"
              placeholder="Enter cat's name"
              icon="i-lucide-cat"
            />
          </UFormGroup>

          <!-- Breed Field -->
          <UFormGroup label="Breed" name="breed">
            <USelectMenu
              v-model="formState.breed"
              :options="breedOptions"
              placeholder="Select breed"
              searchable
            />
          </UFormGroup>

          <!-- Age Field -->
          <UFormGroup label="Age" name="age">
            <UInput
              v-model="formState.age"
              type="number"
              placeholder="Enter age in years"
              min="0"
              max="30"
              icon="i-lucide-calendar"
              @input="(event: Event) => formState.age = (event.target as HTMLInputElement).value ? Number((event.target as HTMLInputElement).value) : undefined"
            />
          </UFormGroup>

          <!-- Color Field -->
          <UFormGroup label="Color" name="color">
            <USelectMenu
              v-model="formState.color"
              :options="colorOptions"
              placeholder="Select color"
              searchable
            />
          </UFormGroup>

          <!-- Description Field -->
          <UFormGroup label="Description" name="description">
            <UTextarea
              v-model="formState.description"
              placeholder="Tell us about this cat (optional)"
              :rows="4"
            />
          </UFormGroup>

          <!-- Submit Button -->
          <div class="flex gap-3 pt-4">
            <UButton
              type="submit"
              icon="i-lucide-save"
              :loading="loading"
              :disabled="loading"
              size="lg"
            >
              Save Changes
            </UButton>
            <UButton
              :to="`/cats/${catId}`"
              variant="outline"
              color="gray"
              size="lg"
            >
              Cancel
            </UButton>
          </div>
          </UForm>
        </ClientOnlyForm>
      </UCard>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
import { CatApi } from '../../../utils/api'
import { updateCatSchema, CAT_BREEDS, CAT_COLORS } from '../../../types/cat'

// Get cat ID from route
const route = useRoute()
const catId = route.params.id as string

// Data fetching
const { data: cat, pending, error } = await useLazyAsyncData(`cat-${catId}`, () => CatApi.getCatById(catId))

// SEO
useSeoMeta({
  title: computed(() => cat.value ? `Edit ${cat.value.name}` : 'Edit Cat'),
  description: computed(() => cat.value ? `Edit details for ${cat.value.name}` : 'Edit cat details')
})

// Form state - initialize with cat data when available
const formState = reactive({
  name: '',
  breed: '',
  age: undefined as number | undefined,
  color: '',
  description: ''
})

// Watch for cat data and populate form
watch(cat, (newCat) => {
  if (newCat) {
    formState.name = newCat.name
    formState.breed = newCat.breed
    formState.age = newCat.age
    formState.color = newCat.color
    formState.description = newCat.description || ''
  }
}, { immediate: true })

const loading = ref(false)

// Options for dropdowns
const breedOptions = CAT_BREEDS.map(breed => ({
  label: breed,
  value: breed
}))

const colorOptions = CAT_COLORS.map(color => ({
  label: color,
  value: color
}))

// Form submission
const onSubmit = async (event: any) => {
  if (!cat.value) return
  
  loading.value = true
  
  try {
    // Only send fields that have changed
    const updateData: any = {}
    
    if (formState.name !== cat.value.name) updateData.name = formState.name
    if (formState.breed !== cat.value.breed) updateData.breed = formState.breed
    if (formState.age !== cat.value.age) updateData.age = formState.age
    if (formState.color !== cat.value.color) updateData.color = formState.color
    if (formState.description !== (cat.value.description || '')) {
      updateData.description = formState.description || undefined
    }

    // If no changes, just navigate back
    if (Object.keys(updateData).length === 0) {
      await navigateTo(`/cats/${catId}`)
      return
    }

    const updatedCat = await CatApi.updateCat(catId, updateData)
    
    const toast = useToast()
    toast.add({
      title: 'Success',
      description: `${updatedCat.name} has been updated successfully`,
      color: 'green'
    })
    
    await navigateTo(`/cats/${catId}`)
  } catch (error: any) {
    const toast = useToast()
    toast.add({
      title: 'Error',
      description: error.message || 'Failed to update cat',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}
</script>
