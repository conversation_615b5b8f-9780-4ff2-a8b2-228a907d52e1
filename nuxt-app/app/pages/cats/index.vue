<template>
  <UContainer class="py-8">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Cat Management
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">
          Manage your feline friends
        </p>
      </div>
      <UButton
        to="/cats/new"
        icon="i-lucide-plus"
        size="lg"
        color="primary"
      >
        Add New Cat
      </UButton>
    </div>

    <!-- Loading State -->
    <div v-if="pending" class="flex justify-center py-12">
      <UIcon name="i-lucide-loader-2" class="w-8 h-8 animate-spin" />
    </div>

    <!-- Error State -->
    <UAlert
      v-else-if="error"
      icon="i-lucide-alert-circle"
      color="red"
      variant="soft"
      title="Error loading cats"
      :description="error.message"
      class="mb-6"
    />

    <!-- Empty State -->
    <UCard v-else-if="!cats?.length" class="text-center py-12">
      <template #header>
        <div class="flex flex-col items-center">
          <UIcon name="i-lucide-cat" class="w-16 h-16 text-gray-400 mb-4" />
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
            No cats found
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mt-2">
            Get started by adding your first cat
          </p>
        </div>
      </template>
      <UButton
        to="/cats/new"
        icon="i-lucide-plus"
        size="lg"
        color="primary"
      >
        Add Your First Cat
      </UButton>
    </UCard>

    <!-- Cats Grid -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <UCard
        v-for="cat in cats"
        :key="cat.id"
        class="hover:shadow-lg transition-shadow cursor-pointer"
        @click="navigateTo(`/cats/${cat.id}`)"
      >
        <template #header>
          <div class="flex justify-between items-start">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ cat.name }}
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ cat.breed }}
              </p>
            </div>
            <UDropdown :items="getDropdownItems(cat.id)">
              <UButton
                icon="i-lucide-more-vertical"
                variant="ghost"
                color="gray"
                size="sm"
                @click.stop
              />
            </UDropdown>
          </div>
        </template>

        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <UIcon name="i-lucide-calendar" class="w-4 h-4 text-gray-500" />
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ cat.age }} {{ cat.age === 1 ? 'year' : 'years' }} old
            </span>
          </div>
          <div class="flex items-center gap-2">
            <UIcon name="i-lucide-palette" class="w-4 h-4 text-gray-500" />
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ cat.color }}
            </span>
          </div>
          <p v-if="cat.description" class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
            {{ cat.description }}
          </p>
        </div>

        <template #footer>
          <div class="flex justify-between items-center text-xs text-gray-500">
            <span>Added {{ formatDate(cat.createdAt) }}</span>
            <span v-if="cat.updatedAt !== cat.createdAt">
              Updated {{ formatDate(cat.updatedAt) }}
            </span>
          </div>
        </template>
      </UCard>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
import { CatApi } from '../../utils/api'
import type { CatResponse } from '../../types/cat'

// SEO
useSeoMeta({
  title: 'Cat Management',
  description: 'Manage your feline friends with our cat management system'
})

// Data fetching
const { data: catsData, pending, error, refresh } = await useLazyAsyncData('cats', () => CatApi.getAllCats())

const cats = computed(() => catsData.value?.cats || [])

// Dropdown actions
const getDropdownItems = (catId: string) => [
  [{
    label: 'View Details',
    icon: 'i-lucide-eye',
    click: () => navigateTo(`/cats/${catId}`)
  }],
  [{
    label: 'Edit',
    icon: 'i-lucide-edit',
    click: () => navigateTo(`/cats/${catId}/edit`)
  }],
  [{
    label: 'Delete',
    icon: 'i-lucide-trash-2',
    click: () => deleteCat(catId)
  }]
]

// Delete cat function
const deleteCat = async (catId: string) => {
  const cat = cats.value.find(c => c.id === catId)
  if (!cat) return

  const confirmed = confirm(`Are you sure you want to delete ${cat.name}?`)
  if (!confirmed) return

  try {
    await CatApi.deleteCat(catId)
    await refresh()
    
    const toast = useToast()
    toast.add({
      title: 'Success',
      description: `${cat.name} has been deleted`,
      color: 'green'
    })
  } catch (error: any) {
    const toast = useToast()
    toast.add({
      title: 'Error',
      description: error.message || 'Failed to delete cat',
      color: 'red'
    })
  }
}

// Date formatting
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}
</script>
