import { z } from 'zod'

// Zod validation schemas
export const createCatSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  breed: z.string().min(1, 'Breed is required').max(100, 'Breed must be less than 100 characters'),
  age: z.number().int().min(0, 'Age must be a positive number').max(30, 'Age must be realistic'),
  color: z.string().min(1, 'Color is required').max(50, 'Color must be less than 50 characters'),
  description: z.string().optional()
})

export const updateCatSchema = createCatSchema.partial()

export const catIdSchema = z.object({
  id: z.string().uuid('Invalid cat ID format')
})

// TypeScript types derived from Zod schemas
export type CreateCatInput = z.infer<typeof createCatSchema>
export type UpdateCatInput = z.infer<typeof updateCatSchema>
export type CatIdInput = z.infer<typeof catIdSchema>

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface CatResponse {
  id: string
  name: string
  breed: string
  age: number
  color: string
  description?: string
  createdAt: string
  updatedAt: string
}

export interface CatsListResponse {
  cats: CatResponse[]
  total: number
}

// Form state types
export interface CatFormState {
  name: string
  breed: string
  age: number | null
  color: string
  description: string
}

export interface FormErrors {
  name?: string
  breed?: string
  age?: string
  color?: string
  description?: string
  general?: string
}

// Loading states
export interface LoadingState {
  loading: boolean
  error: string | null
}

// Common cat breeds for form dropdown
export const CAT_BREEDS = [
  'Persian',
  'Maine Coon',
  'British Shorthair',
  'Ragdoll',
  'Bengal',
  'Siamese',
  'Abyssinian',
  'Russian Blue',
  'Scottish Fold',
  'Sphynx',
  'American Shorthair',
  'Norwegian Forest Cat',
  'Birman',
  'Oriental Shorthair',
  'Devon Rex',
  'Cornish Rex',
  'Turkish Angora',
  'Manx',
  'Exotic Shorthair',
  'Mixed Breed',
  'Other'
] as const

// Common cat colors
export const CAT_COLORS = [
  'Black',
  'White',
  'Gray',
  'Orange',
  'Brown',
  'Cream',
  'Silver',
  'Calico',
  'Tortoiseshell',
  'Tabby',
  'Tuxedo',
  'Bi-color',
  'Tri-color',
  'Other'
] as const
