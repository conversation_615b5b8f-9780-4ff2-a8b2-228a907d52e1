import type { ApiResponse, CatResponse, CatsListResponse, CreateCatInput, UpdateCatInput } from '~/types/cat'

// API utility functions for cat management
export class CatApi {
  private static baseUrl = '/api/cats'

  static async getAllCats(): Promise<CatsListResponse> {
    const response = await $fetch<ApiResponse<CatsListResponse>>(`${this.baseUrl}`)
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch cats')
    }
    return response.data!
  }

  static async getCatById(id: string): Promise<CatResponse> {
    const response = await $fetch<ApiResponse<CatResponse>>(`${this.baseUrl}/${id}`)
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch cat')
    }
    return response.data!
  }

  static async createCat(catData: CreateCatInput): Promise<CatResponse> {
    const response = await $fetch<ApiResponse<CatResponse>>(`${this.baseUrl}`, {
      method: 'POST',
      body: catData
    })
    if (!response.success) {
      throw new Error(response.error || 'Failed to create cat')
    }
    return response.data!
  }

  static async updateCat(id: string, catData: UpdateCatInput): Promise<CatResponse> {
    const response = await $fetch<ApiResponse<CatResponse>>(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      body: catData
    })
    if (!response.success) {
      throw new Error(response.error || 'Failed to update cat')
    }
    return response.data!
  }

  static async deleteCat(id: string): Promise<void> {
    const response = await $fetch<ApiResponse>(`${this.baseUrl}/${id}`, {
      method: 'DELETE'
    })
    if (!response.success) {
      throw new Error(response.error || 'Failed to delete cat')
    }
  }
}

// Error handling utility
export function handleApiError(error: any): string {
  if (error?.data?.error) {
    return error.data.error
  }
  if (error?.message) {
    return error.message
  }
  return 'An unexpected error occurred'
}
