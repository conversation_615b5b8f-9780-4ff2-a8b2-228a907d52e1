export default defineNuxtPlugin(() => {
  // Global error handler for unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    const toast = useToast()
    toast.add({
      title: 'Unexpected Error',
      description: 'An unexpected error occurred. Please try again.',
      color: 'red'
    })
    
    // Prevent the default browser error handling
    event.preventDefault()
  })

  // Global error handler for JavaScript errors
  window.addEventListener('error', (event) => {
    console.error('JavaScript error:', event.error)
    
    const toast = useToast()
    toast.add({
      title: 'Application Error',
      description: 'An error occurred in the application. Please refresh the page.',
      color: 'red'
    })
  })
})
