# Cat Management System

A full-stack CRUD application for managing cats built with Nuxt 4, Supa<PERSON>, and Drizzle ORM.

## Features

- ✨ **Full CRUD Operations**: Create, read, update, and delete cat records
- 🎨 **Modern UI**: Built with <PERSON>uxt UI and Tailwind CSS
- 🔒 **Type Safety**: TypeScript throughout with shared types
- 🗄️ **Database**: Supabase with Drizzle ORM for type-safe database operations
- 📱 **Responsive Design**: Works on desktop and mobile devices
- ⚡ **Server-Side Rendering**: Powered by <PERSON>uxt 4
- 🔄 **Real-time Updates**: Optimistic UI updates with proper error handling

## Tech Stack

- **Frontend**: Nuxt 4, Vue 3, TypeScript
- **UI Components**: Nuxt UI, Tailwind CSS
- **Backend**: Nuxt Server API Routes
- **Database**: Supabase (PostgreSQL)
- **ORM**: Drizzle ORM
- **Validation**: Zod

## Quick Start

### Prerequisites

- Node.js 18+ 
- pnpm (recommended) or npm
- Supabase account

### 1. <PERSON><PERSON> and <PERSON>stall

```bash
git clone <your-repo-url>
cd nuxt-app
pnpm install
```

### 2. Database Setup

1. Create a new project at [Supabase](https://supabase.com)
2. Copy your project URL and anon key from the API settings
3. Copy the database URL from the database settings

### 3. Environment Configuration

Create a `.env` file in the root directory:

```bash
# Copy from .env.example
cp .env.example .env
```

Update the `.env` file with your Supabase credentials:

```bash
NUXT_UI_PRO_LICENSE=your-license-key
SUPABASE_URL=your-supabase-project-url
SUPABASE_ANON_KEY=your-supabase-anon-key
DATABASE_URL=your-supabase-database-url
```

### 4. Database Migration

Run the database migration to create the cats table:

```bash
pnpm db:migrate
```

### 5. Start Development

```bash
pnpm dev
```

Visit `http://localhost:3000` to see your application!

## Available Scripts

```bash
# Development
pnpm dev

# Build for production
pnpm build

# Preview production build
pnpm preview

# Database operations
pnpm db:generate    # Generate migrations
pnpm db:migrate     # Run migrations
pnpm db:studio      # Open Drizzle Studio

# Code quality
pnpm lint           # Run ESLint
pnpm typecheck      # Run TypeScript checks
```

## Project Structure

```
nuxt-app/
├── app/
│   ├── pages/
│   │   ├── index.vue           # Home page
│   │   └── cats/
│   │       ├── index.vue       # Cats listing
│   │       ├── new.vue         # Add new cat
│   │       ├── [id].vue        # Cat details
│   │       └── [id]/edit.vue   # Edit cat
│   └── app.vue                 # Root component
├── server/
│   └── api/
│       └── cats/               # API endpoints
├── lib/
│   ├── db/
│   │   ├── schema.ts           # Database schema
│   │   ├── index.ts            # Database connection
│   │   └── migrations/         # Database migrations
│   └── supabase.ts             # Supabase client
├── types/
│   └── cat.ts                  # TypeScript types
├── utils/
│   └── api.ts                  # API utilities
├── composables/
│   ├── useCatForm.ts           # Form handling
│   └── useCatOperations.ts     # CRUD operations
└── plugins/
    └── error-handler.client.ts # Global error handling
```

## API Endpoints

- `GET /api/cats` - Get all cats
- `POST /api/cats` - Create a new cat
- `GET /api/cats/[id]` - Get cat by ID
- `PUT /api/cats/[id]` - Update cat by ID
- `DELETE /api/cats/[id]` - Delete cat by ID

## Database Schema

The `cats` table includes:

- `id` (UUID, Primary Key)
- `name` (VARCHAR, Required)
- `breed` (VARCHAR, Required)
- `age` (INTEGER, Required)
- `color` (VARCHAR, Required)
- `description` (TEXT, Optional)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
