import { seedDatabase } from '../../lib/db/seed.js'

export default async function () {
  // Only run seeding in development mode
  if (process.env.NODE_ENV === 'development') {
    try {
      await seedDatabase()
    } catch (error) {
      console.error('Failed to seed database on startup:', error)
      // Don't throw error to prevent server from crashing
      // The application should still work even if seeding fails
    }
  }
}
