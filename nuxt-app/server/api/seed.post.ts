import { seedDatabase } from '../../lib/db/seed.js'

export default defineEventHandler(async (event) => {
  try {
    const insertedCats = await seedDatabase()
    
    return {
      success: true,
      message: `Successfully seeded ${insertedCats.length} cats`,
      data: insertedCats
    }
  } catch (error: any) {
    console.error('Error in seed endpoint:', error)
    
    return {
      success: false,
      error: error.message || 'Failed to seed database'
    }
  }
})
