import { db } from '../../../lib/db/index.js'
import { cats } from '../../../lib/db/schema.js'
import { eq } from 'drizzle-orm'
import { catIdSchema } from '../../../app/types/cat.js'
import type { ApiResponse } from '../../../app/types/cat.js'

export default defineEventHandler(async (event): Promise<ApiResponse> => {
  try {
    const id = getRouterParam(event, 'id')
    
    // Validate ID format
    const validationResult = catIdSchema.safeParse({ id })
    if (!validationResult.success) {
      return {
        success: false,
        error: 'Invalid cat ID format'
      }
    }

    // Check if cat exists
    const [existingCat] = await db.select().from(cats).where(eq(cats.id, id!))
    if (!existingCat) {
      return {
        success: false,
        error: 'Cat not found'
      }
    }

    // Delete cat from database
    await db.delete(cats).where(eq(cats.id, id!))

    return {
      success: true,
      message: 'Cat deleted successfully'
    }
  } catch (error) {
    console.error('Error deleting cat:', error)
    return {
      success: false,
      error: 'Failed to delete cat'
    }
  }
})
