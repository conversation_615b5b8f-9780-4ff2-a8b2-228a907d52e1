import { db } from '../../../lib/db/index.js'
import { cats } from '../../../lib/db/schema.js'
import { eq } from 'drizzle-orm'
import { catIdSchema } from '../../../app/types/cat.js'
import type { ApiResponse, CatResponse } from '../../../app/types/cat.js'

export default defineEventHandler(async (event): Promise<ApiResponse<CatResponse>> => {
  try {
    const id = getRouterParam(event, 'id')
    
    // Validate ID format
    const validationResult = catIdSchema.safeParse({ id })
    if (!validationResult.success) {
      return {
        success: false,
        error: 'Invalid cat ID format'
      }
    }

    // Find cat by ID
    const [cat] = await db.select().from(cats).where(eq(cats.id, id!))
    
    if (!cat) {
      return {
        success: false,
        error: 'Cat not found'
      }
    }

    const response: CatResponse = {
      id: cat.id,
      name: cat.name,
      breed: cat.breed,
      age: cat.age,
      color: cat.color,
      description: cat.description || undefined,
      createdAt: cat.createdAt.toISOString(),
      updatedAt: cat.updatedAt.toISOString()
    }

    return {
      success: true,
      data: response
    }
  } catch (error) {
    console.error('Error fetching cat:', error)
    return {
      success: false,
      error: 'Failed to fetch cat'
    }
  }
})
