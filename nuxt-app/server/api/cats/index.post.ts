import { db } from '../../../lib/db/index.js'
import { cats } from '../../../lib/db/schema.js'
import { createCatSchema } from '../../../app/types/cat.js'
import type { ApiResponse, CatResponse } from '../../../app/types/cat.js'

export default defineEventHandler(async (event): Promise<ApiResponse<CatResponse>> => {
  try {
    const body = await readBody(event)
    
    // Validate input data
    const validationResult = createCatSchema.safeParse(body)
    if (!validationResult.success) {
      return {
        success: false,
        error: validationResult.error.issues.map((e: any) => e.message).join(', ')
      }
    }

    const { name, breed, age, color, description } = validationResult.data

    // Insert new cat into database
    const [newCat] = await db.insert(cats).values({
      name,
      breed,
      age,
      color,
      description: description || null
    }).returning()

    const response: CatResponse = {
      id: newCat.id,
      name: newCat.name,
      breed: newCat.breed,
      age: newCat.age,
      color: newCat.color,
      description: newCat.description || undefined,
      createdAt: newCat.createdAt.toISOString(),
      updatedAt: newCat.updatedAt.toISOString()
    }

    return {
      success: true,
      data: response,
      message: 'Cat created successfully'
    }
  } catch (error) {
    console.error('Error creating cat:', error)
    return {
      success: false,
      error: 'Failed to create cat'
    }
  }
})
