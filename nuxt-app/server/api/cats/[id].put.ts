import { db } from '../../../lib/db/index.js'
import { cats } from '../../../lib/db/schema.js'
import { eq, sql } from 'drizzle-orm'
import { updateCatSchema, catIdSchema } from '../../../app/types/cat.js'
import type { ApiResponse, CatResponse } from '../../../app/types/cat.js'

export default defineEventHandler(async (event): Promise<ApiResponse<CatResponse>> => {
  try {
    const id = getRouterParam(event, 'id')
    const body = await readBody(event)
    
    // Validate ID format
    const idValidation = catIdSchema.safeParse({ id })
    if (!idValidation.success) {
      return {
        success: false,
        error: 'Invalid cat ID format'
      }
    }

    // Validate input data
    const validationResult = updateCatSchema.safeParse(body)
    if (!validationResult.success) {
      return {
        success: false,
        error: validationResult.error.issues.map((e: any) => e.message).join(', ')
      }
    }

    const updateData = validationResult.data

    // Check if cat exists
    const [existingCat] = await db.select().from(cats).where(eq(cats.id, id!))
    if (!existingCat) {
      return {
        success: false,
        error: 'Cat not found'
      }
    }

    // Update cat in database
    const [updatedCat] = await db
      .update(cats)
      .set({
        ...updateData,
        updatedAt: sql`now()`
      })
      .where(eq(cats.id, id!))
      .returning()

    const response: CatResponse = {
      id: updatedCat.id,
      name: updatedCat.name,
      breed: updatedCat.breed,
      age: updatedCat.age,
      color: updatedCat.color,
      description: updatedCat.description || undefined,
      createdAt: updatedCat.createdAt.toISOString(),
      updatedAt: updatedCat.updatedAt.toISOString()
    }

    return {
      success: true,
      data: response,
      message: 'Cat updated successfully'
    }
  } catch (error) {
    console.error('Error updating cat:', error)
    return {
      success: false,
      error: 'Failed to update cat'
    }
  }
})
