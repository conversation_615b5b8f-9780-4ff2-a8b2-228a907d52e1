import { db } from '../../../lib/db/index.js'
import { cats } from '../../../lib/db/schema.js'
import type { ApiResponse, CatsListResponse } from '../../../app/types/cat.js'

export default defineEventHandler(async (event): Promise<ApiResponse<CatsListResponse>> => {
  try {
    const allCats = await db.select().from(cats).orderBy(cats.createdAt)
    
    const response: CatsListResponse = {
      cats: allCats.map(cat => ({
        id: cat.id,
        name: cat.name,
        breed: cat.breed,
        age: cat.age,
        color: cat.color,
        description: cat.description || undefined,
        createdAt: cat.createdAt.toISOString(),
        updatedAt: cat.updatedAt.toISOString()
      })),
      total: allCats.length
    }

    return {
      success: true,
      data: response
    }
  } catch (error) {
    console.error('Error fetching cats:', error)
    return {
      success: false,
      error: 'Failed to fetch cats'
    }
  }
})
