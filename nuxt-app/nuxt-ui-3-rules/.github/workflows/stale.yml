name: stale

on:
  workflow_dispatch:
  schedule:
    - cron: '30 1 * * *'

jobs:
  stale:
    runs-on: ubuntu-latest

    permissions:
      actions: write
      issues: write

    steps:
      - uses: actions/stale@4c023f01d613e60293d8004f251a18bfb9bbd71d
        with:
          days-before-pr-stale: -1
          days-before-stale: 60
          days-before-close: 7
          stale-issue-label: 'stale'
          close-issue-label: 'closed-by-bot'
          close-issue-message: |
            Hi! 👋

            This issue has been automatically **closed** due to prolonged inactivity.

            We're a small team and can't address every report, but we appreciate your feedback and contributions.

            If this issue is still relevant with the latest version of Nuxt UI, please feel free to reopen or create a new issue with updated details.

            Thank you for your understanding and support!

            — Nuxt UI Team
          exempt-issue-labels: 'feature,announcement,release,reka-ui,upstream'
          operations-per-run: 300
