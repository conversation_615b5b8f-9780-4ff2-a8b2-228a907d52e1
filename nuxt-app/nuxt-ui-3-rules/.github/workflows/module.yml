name: module

on:
  push:
    branches:
      - v3
  pull_request:
    branches:
      - v3

jobs:
  build:
    runs-on: ${{ matrix.os }}

    permissions:
      contents: read
      pull-requests: read

    strategy:
      matrix:
        os: ${{ github.event_name == 'pull_request' && fromJSON('["ubuntu-latest"]') || fromJSON('["ubuntu-latest", "windows-latest"]') }} # macos-latest
        node: [22]

    env:
      NUXT_GITHUB_TOKEN: ${{ secrets.NUXT_GITHUB_TOKEN }}

    steps:
      - name: Checkout
        uses: actions/checkout@v5

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Install node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install

      - name: Prepare
        run: pnpm run dev:prepare

      - name: Lint
        run: pnpm run lint

      - name: Typecheck
        run: pnpm run typecheck

      - name: Test
        run: pnpm run test run

      - name: Test (vue)
        run: pnpm run test:vue run

      - name: Build
        run: pnpm run build

      - name: Build playground
        run: pnpm run dev:build

      - name: Build playground (vue)
        run: pnpm run dev:vue:build

      - name: Publish
        # Only publish preview package on ubuntu during PRs
        if: matrix.os == 'ubuntu-latest'
        run: pnpx pkg-pr-new publish --compact --no-template --pnpm

  playground:
    needs: build

    runs-on: ${{ matrix.os }}

    defaults:
      run:
        working-directory: ./playground

    permissions:
      contents: read
      pull-requests: read

    strategy:
      matrix:
        os: [ubuntu-latest] # macos-latest, windows-latest
        node: [22]

    steps:
      - name: Checkout
        uses: actions/checkout@v5

      - name: Store commit SHA
        run: |
          echo "COMMIT_SHA=$(echo ${{ github.event.pull_request.head.sha || github.sha }} | cut -c1-7)" >> $GITHUB_ENV

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Install node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: pnpm

      - name: Install latest nuxt/ui
        run: pnpm install https://pkg.pr.new/@nuxt/ui@${{ env.COMMIT_SHA }} --lockfile-only

      - name: Install dependencies
        run: pnpm install --ignore-workspace

      - name: Prepare
        run: pnpm nuxt prepare

      - name: Typecheck
        run: pnpm run typecheck

  starter-nuxt:
    needs: build

    runs-on: ${{ matrix.os }}

    permissions:
      contents: read
      pull-requests: read

    strategy:
      matrix:
        os: [ubuntu-latest] # macos-latest, windows-latest
        node: [22]

    steps:
      - name: Checkout
        uses: actions/checkout@v5
        with:
          repository: nuxtlabs/nuxt-ui-starter

      - name: Store commit SHA
        run: |
          echo "COMMIT_SHA=$(echo ${{ github.event.pull_request.head.sha || github.sha }} | cut -c1-7)" >> $GITHUB_ENV

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Install node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: pnpm

      - name: Install latest nuxt/ui
        run: pnpm install https://pkg.pr.new/@nuxt/ui@${{ env.COMMIT_SHA }} --lockfile-only

      - name: Install dependencies
        run: pnpm install

      - name: Typecheck
        run: pnpm run typecheck

      - name: Build
        run: pnpm run build

  starter-vue:
    needs: build

    runs-on: ${{ matrix.os }}

    permissions:
      contents: read
      pull-requests: read

    strategy:
      matrix:
        os: [ubuntu-latest] # macos-latest, windows-latest
        node: [22]

    steps:
      - name: Checkout
        uses: actions/checkout@v5
        with:
          repository: nuxtlabs/nuxt-ui-vue-starter

      - name: Store commit SHA
        run: |
          echo "COMMIT_SHA=$(echo ${{ github.event.pull_request.head.sha || github.sha }} | cut -c1-7)" >> $GITHUB_ENV

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Install node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: pnpm

      - name: Install latest nuxt/ui
        run: pnpm install https://pkg.pr.new/@nuxt/ui@${{ env.COMMIT_SHA }} --lockfile-only

      - name: Install dependencies
        run: pnpm install

      - name: Typecheck
        run: pnpm run typecheck

      - name: Build
        run: pnpm run build

  nuxt-ui-pro:
    needs: build

    # Only run this job if not a fork PR (when push event or PR from same repo)
    if: github.event_name == 'push' || github.event.pull_request.head.repo.full_name == github.repository

    runs-on: ${{ matrix.os }}

    permissions:
      contents: read
      pull-requests: read

    strategy:
      matrix:
        os: [ubuntu-latest] # macos-latest, windows-latest
        node: [22]

    env:
      NUXT_UI_PRO_LICENSE: ${{ secrets.NUXT_UI_PRO_LICENSE }}

    steps:
      - name: Checkout
        uses: actions/checkout@v5
        with:
          repository: nuxt/ui-pro
          token: ${{ secrets.NUXT_GITHUB_TOKEN }}

      - name: Store commit SHA
        run: |
          echo "COMMIT_SHA=$(echo ${{ github.event.pull_request.head.sha || github.sha }} | cut -c1-7)" >> $GITHUB_ENV

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Install node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: pnpm

      - name: Install latest nuxt/ui
        run: pnpm install https://pkg.pr.new/@nuxt/ui@${{ env.COMMIT_SHA }} --lockfile-only

      - name: Install dependencies
        run: pnpm install

      - name: Prepare
        run: pnpm run dev:prepare

      - name: Typecheck
        run: pnpm run typecheck

      - name: Build
        run: pnpm run build
