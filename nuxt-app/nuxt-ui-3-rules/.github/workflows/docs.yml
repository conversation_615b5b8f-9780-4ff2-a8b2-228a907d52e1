name: docs

on: push

jobs:
  deploy:
    runs-on: ${{ matrix.os }}

    permissions:
      contents: read
      id-token: write

    strategy:
      matrix:
        os: [ubuntu-latest] # macos-latest, windows-latest
        node: [22]

    env:
      NUXT_GITHUB_TOKEN: ${{ secrets.NUXT_GITHUB_TOKEN }}

    steps:
      - uses: actions/checkout@v5
        with:
          fetch-depth: 0

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Install node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install

      - name: Prepare build
        run: pnpm run dev:prepare

      - name: Deploy to NuxtHub
        uses: nuxt-hub/action@v2
        env:
          NODE_OPTIONS: '--max-old-space-size=8192'
        with:
          project-key: ui-7eg3
          directory: docs
