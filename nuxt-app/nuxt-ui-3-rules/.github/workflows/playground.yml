name: playground

on:
  push:
    branches:
      - v3

jobs:
  deploy:
    runs-on: ${{ matrix.os }}

    permissions:
      contents: read
      id-token: write

    strategy:
      matrix:
        os: [ubuntu-latest] # macos-latest, windows-latest
        node: [22]

    steps:
      - uses: actions/checkout@v5

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Install node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install

      - name: Prepare build
        run: pnpm run dev:prepare

      - name: Deploy to NuxtHub
        uses: nuxt-hub/action@v2
        env:
          NODE_OPTIONS: '--max-old-space-size=8192'
        with:
          project-key: ui3-playground-pb9b
          directory: playground
