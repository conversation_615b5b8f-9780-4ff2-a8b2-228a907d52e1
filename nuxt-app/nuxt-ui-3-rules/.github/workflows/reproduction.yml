name: reproduction

on:
  workflow_dispatch:
  schedule:
    - cron: '30 1 * * *'

jobs:
  reproduction:
    runs-on: ubuntu-latest

    permissions:
      actions: write
      issues: write

    steps:
      - uses: actions/stale@v9
        with:
          days-before-stale: -1 # Issues and PR will never be flagged stale automatically.
          stale-issue-label: 'needs reproduction' # Label that flags an issue as stale.
          only-labels: 'needs reproduction' # Only process these issues
          days-before-issue-close: 7
          ignore-updates: true
          remove-stale-when-updated: false
          close-issue-message: This issue was closed because it was open for 7 days without a reproduction.
          close-issue-label: closed-by-bot
          operations-per-run: 300 #default 30
