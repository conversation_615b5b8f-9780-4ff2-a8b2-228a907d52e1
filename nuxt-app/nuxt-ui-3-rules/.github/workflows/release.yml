name: release

on:
  push:
    tags:
      - 'v3*'

jobs:
  publish:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest] # macos-latest, windows-latest
        node: [22]

    env:
      NUXT_GITHUB_TOKEN: ${{ secrets.NUXT_GITHUB_TOKEN }}

    steps:
      - name: Checkout
        uses: actions/checkout@v5

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Install node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install

      - name: Prepare
        run: pnpm run dev:prepare

      - name: Lint
        run: pnpm run lint

      - name: Typecheck
        run: pnpm run typecheck

      - name: Test
        run: pnpm run test run

      - name: Test (vue)
        run: pnpm run test:vue run

      - name: Publish
        run: ./scripts/release.sh
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NODE_AUTH_TOKEN }}
