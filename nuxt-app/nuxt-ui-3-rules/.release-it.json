{"git": {"commitMessage": "chore(release): v${version}", "tagName": "v${version}"}, "npm": {"publish": false}, "github": {"release": true, "releaseName": "v${version}", "web": true}, "hooks": {"before:init": ["pnpm lint", "pnpm typecheck"]}, "plugins": {"@release-it/conventional-changelog": {"preset": {"name": "conventionalcommits"}, "infile": "CHANGELOG.md", "header": "# Changelog", "ignoreRecommendedBump": true}}}