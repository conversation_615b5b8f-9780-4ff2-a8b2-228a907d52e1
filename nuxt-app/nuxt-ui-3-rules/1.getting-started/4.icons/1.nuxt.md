---
title: Icons
description: 'Nuxt UI integrates with Nuxt Icon to access over 200,000+ icons from Iconify.'
framework: nuxt
links:
  - label: 'Iconify'
    to: https://iconify.design/
    target: _blank
    icon: i-simple-icons-iconify
  - label: 'nuxt/icon'
    to: https://github.com/nuxt/icon
    target: _blank
    icon: i-simple-icons-github
navigation.icon: i-lucide-smile
---

::callout{to="/getting-started/icons/vue" icon="i-logos-vue" class="hidden"}
Looking for the **Vue** version?
::

## Usage

Nuxt UI automatically registers the [`@nuxt/icon`](https://github.com/nuxt/icon) module for you, so there's no additional setup required.

### Icon Component

You can use the [Icon](/components/icon) component with a `name` prop to display an icon:

::component-code{slug="icon"}
---
props:
  name: 'i-lucide-lightbulb'
  class: 'size-5'
---
::

::note
You can use any name from the <https://icones.js.org> collection.
::

### Component Props

Some components also have an `icon` prop to display an icon, like the [Button](/components/button) for example:

::component-code{slug="button"}
---
ignore:
  - color
  - variant
props:
  icon: i-lucide-sun
  variant: subtle
slots:
  default: Button
---
::

## Collections

### Iconify Dataset

It's highly recommended to install the icon data locally with:

::code-group{sync="pm"}

```bash [pnpm]
pnpm i @iconify-json/{collection_name}
```

```bash [yarn]
yarn add @iconify-json/{collection_name}
```

```bash [npm]
npm install @iconify-json/{collection_name}
```

::

For example, to use the `i-uil-github` icon, install it's collection with `@iconify-json/uil`. This way the icons can be served locally or from your serverless functions, which is faster and more reliable on both SSR and client-side.

::note{to="https://github.com/nuxt/icon?tab=readme-ov-file#iconify-dataset" target="_blank"}
Read more about this in the `@nuxt/icon` documentation.
::

### Custom Local Collections

You can use local SVG files to create a custom Iconify collection.

For example, place your icons' SVG files under a folder of your choice, for example, `./app/assets/icons`:

```bash
assets/icons
├── add.svg
└── remove.svg
```

In your `nuxt.config.ts`, add an item in `icon.customCollections`:

```ts
export default defineNuxtConfig({
  modules: ['@nuxt/ui'],
  css: ['~/assets/css/main.css'],
  icon: {
    customCollections: [{
      prefix: 'custom',
      dir: './app/assets/icons'
    }]
  }
})
```

Then you can use the icons like this:

```vue
<template>
  <UIcon name="i-custom-add" />
</template>
```

::note{to="https://github.com/nuxt/icon?tab=readme-ov-file#custom-local-collections" target="_blank"}
Read more about this in the `@nuxt/icon` documentation.
::

## Theme

You can change the default icons used by components in your `app.config.ts`:

:icons-theme
