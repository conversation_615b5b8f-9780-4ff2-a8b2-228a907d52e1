---
title: Icons
description: 'Nuxt UI integrates with Iconify to access over 200,000+ icons.'
framework: vue
links:
  - label: 'Iconify'
    to: https://iconify.design/
    target: _blank
    icon: i-simple-icons-iconify
navigation.icon: i-lucide-smile
---

::callout{to="/getting-started/icons/nuxt" icon="i-logos-nuxt-icon" class="hidden"}
Looking for the **Nuxt** version?
::

## Usage

### Icon Component

You can use the [Icon](/components/icon) component with a `name` prop to display an icon:

::component-code{slug="icon"}
---
props:
  name: 'i-lucide-lightbulb'
  class: 'size-5'
---
::

::note
You can use any name from the <https://icones.js.org> collection.
::

::warning
When using collections with a dash (`-`), you need to separate the icon name from the collection name with a colon (`:`) as `@iconify/vue` does not handle this case like `@nuxt/icon`. For example, instead of `i-simple-icons-github` you need to write `i-simple-icons:github` or `simple-icons:github`.

Learn more about the [Iconify naming convention](https://iconify.design/docs/icon-components/vue/#icon).
::

### Component Props

Some components also have an `icon` prop to display an icon, like the [Button](/components/button) for example:

::component-code{slug="button"}
---
ignore:
  - color
  - variant
props:
  icon: i-lucide-sun
  variant: subtle
slots:
  default: Button
---
::

## Theme

You can change the default icons used by Nuxt UI components in your `vite.config.ts`:

:icons-theme
