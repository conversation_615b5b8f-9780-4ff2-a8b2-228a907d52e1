---
title: Fonts
description: 'Nuxt UI integrates with Nuxt Fonts to provide plug-and-play font optimization.'
framework: nuxt
links:
  - label: 'nuxt/fonts'
    to: https://github.com/nuxt/fonts
    target: _blank
    icon: i-simple-icons-github
navigation.icon: i-lucide-a-large-small
---

## Usage

Nuxt UI automatically registers the [`@nuxt/fonts`](https://github.com/nuxt/fonts) module for you, so there's no additional setup required. To use a font in your Nuxt UI application, you can simply declare it in your CSS. It will be automatically loaded and optimized for you.

::module-only
#ui
:::div

```css [app/assets/css/main.css]
@import "tailwindcss";
@import "@nuxt/ui";

@theme {
  --font-sans: 'Public Sans', sans-serif;
}
```

:::

#ui-pro
:::div

```css [app/assets/css/main.css]
@import "tailwindcss";
@import "@nuxt/ui-pro";

@theme {
  --font-sans: 'Public Sans', sans-serif;
}
```

:::
::

You can disable the `@nuxt/fonts` module with the `ui.fonts` option in your `nuxt.config.ts`:

```ts [nuxt.config.ts]
export default defineNuxtConfig({
  ui: {
    fonts: false
  }
})
```
