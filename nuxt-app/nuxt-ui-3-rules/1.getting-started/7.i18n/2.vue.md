---
title: Internationalization (i18n)
description: 'Learn how to internationalize your Vue app with multi-directional support (LTR/RTL).'
framework: vue
navigation.icon: i-lucide-languages
---

::callout{to="/getting-started/i18n/nuxt" icon="i-logos-nuxt-icon" class="hidden"}
Looking for the **Nuxt** version?
::

## Usage

::note{to="/components/app"}
Nuxt UI provides an **App** component that wraps your app to provide global configurations.
::

### Locale

::module-only

#ui
:::div

Use the `locale` prop with the locale you want to use from `@nuxt/ui/locale`:

```vue [App.vue]
<script setup lang="ts">
import { fr } from '@nuxt/ui/locale'
</script>

<template>
  <UApp :locale="fr">
    <RouterView />
  </UApp>
</template>
```

:::

#ui-pro
:::div

Use the `locale` prop with the locale you want to use from `@nuxt/ui-pro/locale`:

```vue [App.vue]
<script setup lang="ts">
import { fr } from '@nuxt/ui-pro/locale'
</script>

<template>
  <UApp :locale="fr">
    <RouterView />
  </UApp>
</template>
```

:::
::

### Custom locale

You can create your own locale using the `defineLocale` composable:

::module-only

#ui
:::div

```vue [App.vue]
<script setup lang="ts">
import type { Messages } from '@nuxt/ui'
import { defineLocale } from '@nuxt/ui/composables/defineLocale.js'

const locale = defineLocale<Messages>({
  name: 'My custom locale',
  code: 'en',
  dir: 'ltr',
  messages: {
    // implement pairs
  }
})
</script>

<template>
  <UApp :locale="locale">
    <RouterView />
  </UApp>
</template>
```

:::

#ui-pro
:::div

```vue [App.vue]
<script setup lang="ts">
import type { Messages } from '@nuxt/ui-pro'
import { defineLocale } from '@nuxt/ui/composables/defineLocale.js'

const locale = defineLocale<Messages>({
  name: 'My custom locale',
  code: 'en',
  dir: 'ltr',
  messages: {
    // implement pairs
  }
})
</script>

<template>
  <UApp :locale="locale">
    <RouterView />
  </UApp>
</template>
```

:::
::

::tip
Look at the `code` parameter, there you need to pass the iso code of the language. Example:

* `hi` Hindi (language)
* `de-AT`: German (language) as used in Austria (region)

::

### Extend locale

You can customize an existing locale by overriding its `messages` or `code` using the `extendLocale` composable:

::module-only

#ui
:::div

```vue [App.vue]
<script setup lang="ts">
import { en } from '@nuxt/ui/locale'
import { extendLocale } from '@nuxt/ui/composables/defineLocale.js'

const locale = extendLocale(en, {
  code: 'en-GB',
  messages: {
    commandPalette: {
      placeholder: 'Search a component...'
    }
  }
})
</script>

<template>
  <UApp :locale="locale">
    <RouterView />
  </UApp>
</template>
```

:::

#ui-pro
:::div

```vue [App.vue]
<script setup lang="ts">
import { en } from '@nuxt/ui-pro/locale'
import { extendLocale } from '@nuxt/ui/composables/defineLocale.js'

const locale = extendLocale(en, {
  code: 'en-GB',
  messages: {
    commandPalette: {
      placeholder: 'Search a component...'
    }
  }
})
</script>

<template>
  <UApp :locale="locale">
    <RouterView />
  </UApp>
</template>
```

:::
::

### Dynamic locale

To dynamically switch between languages, you can use the [Vue I18n](https://vue-i18n.intlify.dev/) plugin.

::steps{level="4"}

#### Install the Vue I18n package

::code-group{sync="pm"}

```bash [pnpm]
pnpm add vue-i18n@11
```

```bash [yarn]
yarn add vue-i18n@11
```

```bash [npm]
npm install vue-i18n@11
```

```bash [bun]
bun add vue-i18n@11
```

::

#### Use the Vue I18n plugin in your `main.ts`

```ts [main.ts]{3,14-26,29}
import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { createI18n } from 'vue-i18n'
import ui from '@nuxt/ui/vue-plugin'
import App from './App.vue'

const app = createApp(App)

const router = createRouter({
  routes: [],
  history: createWebHistory()
})

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  availableLocales: ['en', 'de'],
  messages: {
    en: {
      // ...
    },
    de: {
      // ...
    }
  }
})

app.use(router)
app.use(i18n)
app.use(ui)

app.mount('#app')
```

#### Set the `locale` prop using `useI18n`

::module-only

#ui
:::div

```vue [App.vue]
<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import * as locales from '@nuxt/ui/locale'

const { locale } = useI18n()
</script>

<template>
  <UApp :locale="locales[locale]">
    <RouterView />
  </UApp>
</template>
```

:::

#ui-pro
:::div

```vue [App.vue]
<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import * as locales from '@nuxt/ui-pro/locale'

const { locale } = useI18n()
</script>

<template>
  <UApp :locale="locales[locale]">
    <RouterView />
  </UApp>
</template>
```

:::
::

::

### Dynamic direction

Each locale has a `dir` property which will be used by the `App` component to set the directionality of all components.

In a multilingual application, you might want to set the `lang` and `dir` attributes on the `<html>` element dynamically based on the user's locale, which you can do with the [useHead](https://unhead.unjs.io/usage/composables/use-head) composable:

::module-only

#ui
:::div

```vue [App.vue]
<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useHead } from '@unhead/vue'
import * as locales from '@nuxt/ui/locale'

const { locale } = useI18n()

const lang = computed(() => locales[locale.value].code)
const dir = computed(() => locales[locale.value].dir)

useHead({
  htmlAttrs: {
    lang,
    dir
  }
})
</script>

<template>
  <UApp :locale="locales[locale]">
    <RouterView />
  </UApp>
</template>
```

:::

#ui-pro
:::div

```vue [App.vue]
<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useHead } from '@unhead/vue'
import * as locales from '@nuxt/ui-pro/locale'

const { locale } = useI18n()

const lang = computed(() => locales[locale.value].code)
const dir = computed(() => locales[locale.value].dir)

useHead({
  htmlAttrs: {
    lang,
    dir
  }
})
</script>

<template>
  <UApp :locale="locales[locale]">
    <RouterView />
  </UApp>
</template>
```

:::
::

## Supported languages

:supported-languages
