---
title: Internationalization (i18n)
description: 'Learn how to internationalize your Nuxt app with multi-directional support (LTR/RTL).'
framework: nuxt
navigation.icon: i-lucide-languages
---

::callout{to="/getting-started/i18n/vue" icon="i-logos-vue" class="hidden"}
Looking for the **Vue** version?
::

## Usage

::note{to="/components/app"}
Nuxt UI provides an **App** component that wraps your app to provide global configurations.
::

### Locale

::module-only

#ui
:::div

Use the `locale` prop with the locale you want to use from `@nuxt/ui/locale`:

```vue [app.vue]
<script setup lang="ts">
import { fr } from '@nuxt/ui/locale'
</script>

<template>
  <UApp :locale="fr">
    <NuxtPage />
  </UApp>
</template>
```

:::

#ui-pro
:::div

Use the `locale` prop with the locale you want to use from `@nuxt/ui-pro/locale`:

```vue [app.vue]
<script setup lang="ts">
import { fr } from '@nuxt/ui-pro/locale'
</script>

<template>
  <UApp :locale="fr">
    <NuxtPage />
  </UApp>
</template>
```

:::
::

### Custom locale

You can create your own locale using the `defineLocale` composable:

::module-only

#ui
:::div

```vue [app.vue]
<script setup lang="ts">
import type { Messages } from '@nuxt/ui'

const locale = defineLocale<Messages>({
  name: 'My custom locale',
  code: 'en',
  dir: 'ltr',
  messages: {
    // implement pairs
  }
})
</script>

<template>
  <UApp :locale="locale">
    <NuxtPage />
  </UApp>
</template>
```

:::

#ui-pro
:::div

```vue [app.vue]
<script setup lang="ts">
import type { Messages } from '@nuxt/ui-pro'

const locale = defineLocale<Messages>({
  name: 'My custom locale',
  code: 'en',
  dir: 'ltr',
  messages: {
    // implement pairs
  }
})
</script>

<template>
  <UApp :locale="locale">
    <NuxtPage />
  </UApp>
</template>
```

:::
::

::tip
Look at the `code` parameter, there you need to pass the iso code of the language. Example:

* `hi` Hindi (language)
* `de-AT`: German (language) as used in Austria (region)

::

### Extend locale

You can customize an existing locale by overriding its `messages` or `code` using the `extendLocale` composable:

::module-only

#ui
:::div

```vue [app.vue]
<script setup lang="ts">
import { en } from '@nuxt/ui/locale'

const locale = extendLocale(en, {
  code: 'en-GB',
  messages: {
    commandPalette: {
      placeholder: 'Search a component...'
    }
  }
})
</script>

<template>
  <UApp :locale="locale">
    <NuxtPage />
  </UApp>
</template>
```

:::

#ui-pro
:::div

```vue [app.vue]
<script setup lang="ts">
import { en } from '@nuxt/ui-pro/locale'

const locale = extendLocale(en, {
  code: 'en-GB',
  messages: {
    commandPalette: {
      placeholder: 'Search a component...'
    }
  }
})
</script>

<template>
  <UApp :locale="locale">
    <NuxtPage />
  </UApp>
</template>
```

:::
::

### Dynamic locale

To dynamically switch between languages, you can use the [Nuxt I18n](https://i18n.nuxtjs.org/) module.

::steps{level="4"}

#### Install the Nuxt I18n package

::code-group{sync="pm"}

```bash [pnpm]
pnpm add @nuxtjs/i18n
```

```bash [yarn]
yarn add @nuxtjs/i18n
```

```bash [npm]
npm install @nuxtjs/i18n
```

```bash [bun]
bun add @nuxtjs/i18n
```

::

#### Add the Nuxt I18n module in your `nuxt.config.ts`{lang="ts-type"}

```ts [nuxt.config.ts]
export default defineNuxtConfig({
  modules: [
    '@nuxt/ui',
    '@nuxtjs/i18n'
  ],
  css: ['~/assets/css/main.css'],
  i18n: {
    locales: [{
      code: 'de',
      name: 'Deutsch'
    }, {
      code: 'en',
      name: 'English'
    }, {
      code: 'fr',
      name: 'Français'
    }]
  }
})
```

#### Set the `locale` prop using `useI18n`

::module-only

#ui
:::div

```vue [app.vue]
<script setup lang="ts">
import * as locales from '@nuxt/ui/locale'

const { locale } = useI18n()
</script>

<template>
  <UApp :locale="locales[locale]">
    <NuxtPage />
  </UApp>
</template>
```

:::

#ui-pro
:::div

```vue [app.vue]
<script setup lang="ts">
import * as locales from '@nuxt/ui-pro/locale'

const { locale } = useI18n()
</script>

<template>
  <UApp :locale="locales[locale]">
    <NuxtPage />
  </UApp>
</template>
```

:::
::

::

### Dynamic direction

Each locale has a `dir` property which will be used by the `App` component to set the directionality of all components.

In a multilingual application, you might want to set the `lang` and `dir` attributes on the `<html>` element dynamically based on the user's locale, which you can do with the [useHead](https://nuxt.com/docs/api/composables/use-head) composable:

::module-only

#ui
:::div

```vue [app.vue]
<script setup lang="ts">
import * as locales from '@nuxt/ui/locale'

const { locale } = useI18n()

const lang = computed(() => locales[locale.value].code)
const dir = computed(() => locales[locale.value].dir)

useHead({
  htmlAttrs: {
    lang,
    dir
  }
})
</script>

<template>
  <UApp :locale="locales[locale]">
    <NuxtPage />
  </UApp>
</template>
```

:::

#ui-pro
:::div

```vue [app.vue]
<script setup lang="ts">
import * as locales from '@nuxt/ui-pro/locale'

const { locale } = useI18n()

const lang = computed(() => locales[locale.value].code)
const dir = computed(() => locales[locale.value].dir)

useHead({
  htmlAttrs: {
    lang,
    dir
  }
})
</script>

<template>
  <UApp :locale="locales[locale]">
    <NuxtPage />
  </UApp>
</template>
```

:::
::

## Supported languages

:supported-languages
