---
title: Color Mode
description: 'Nuxt UI integrates with Nuxt Color Mode to allow for easy switching between light and dark themes.'
framework: nuxt
links:
  - label: 'nuxtjs/color-mode'
    to: https://github.com/nuxt-modules/color-mode
    target: _blank
    icon: i-simple-icons-github
navigation.icon: i-lucide-sun-moon
---

::callout{to="/getting-started/color-mode/vue" icon="i-logos-vue" class="hidden"}
Looking for the **Vue** version?
::

## Usage

Nuxt UI automatically registers the [`@nuxtjs/color-mode`](https://github.com/nuxt-modules/color-mode) module for you, so there's no additional setup required. You can simply use the `useColorMode` composable to switch between light and dark modes:

```vue [ColorModeButton.vue]
<script setup>
const colorMode = useColorMode()

const isDark = computed({
  get() {
    return colorMode.value === 'dark'
  },
  set(_isDark) {
    colorMode.preference = _isDark ? 'dark' : 'light'
  }
})
</script>

<template>
  <ClientOnly v-if="!colorMode?.forced">
    <UButton
      :icon="isDark ? 'i-lucide-moon' : 'i-lucide-sun'"
      color="neutral"
      variant="ghost"
      @click="isDark = !isDark"
    />

    <template #fallback>
      <div class="size-8" />
    </template>
  </ClientOnly>
</template>
```

You can disable the `@nuxtjs/color-mode` module with the `ui.colorMode` option in your `nuxt.config.ts`:

```ts [nuxt.config.ts]
export default defineNuxtConfig({
  modules: ['@nuxt/ui'],
  css: ['~/assets/css/main.css'],
  ui: {
    colorMode: false
  }
})
```
