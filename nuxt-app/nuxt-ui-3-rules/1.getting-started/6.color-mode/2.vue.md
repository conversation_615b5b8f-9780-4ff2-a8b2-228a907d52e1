---
title: Color Mode
description: 'Nuxt UI integrates with VueUse to allow for easy switching between light and dark themes.'
framework: vue
navigation.icon: i-lucide-sun-moon
---

::callout{to="/getting-started/color-mode/nuxt" icon="i-logos-nuxt-icon" class="hidden"}
Looking for the **Nuxt** version?
::

## Usage

Nuxt UI automatically registers the [useDark](https://vueuse.org/core/useDark) composable as a Vue plugin, so there's no additional setup required. You can simply use it to switch between light and dark modes:

```vue [ColorModeButton.vue]
<script setup>
import { useColorMode } from '@vueuse/core'

const mode = useColorMode()
</script>

<template>
  <UButton
    :icon="mode === 'dark' ? 'i-lucide-moon' : 'i-lucide-sun'"
    color="neutral"
    variant="ghost"
    @click="mode = mode === 'dark' ? 'light' : 'dark'"
  />
</template>
```

You can disable this plugin with the `colorMode` option in your `vite.config.ts`:

::module-only
#ui
:::div
```ts [vite.config.ts]
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import ui from '@nuxt/ui/vite'

export default defineConfig({
  plugins: [
    vue(),
    ui({
      colorMode: false
    })
  ]
})
```

:::
 
#ui-pro
:::div
```ts [vite.config.ts]
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import uiPro from '@nuxt/ui-pro/vite'

export default defineConfig({
  plugins: [
    vue(),
    uiPro({
      colorMode: false
    })
  ]
})
```
:::
::
