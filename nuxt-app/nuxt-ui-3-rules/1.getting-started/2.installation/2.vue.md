---
title: Installation
description: 'Learn how to install and configure Nuxt UI in your Vue application.'
framework: vue
module: ui
links:
  - label: Playground
    to: https://codesandbox.io/p/devbox/nuxt-ui3-vue-4h5gqn
    icon: i-lucide-codesandbox
navigation.icon: i-lucide-square-play
---

::callout{to="/getting-started/installation/nuxt" icon="i-logos-nuxt-icon" class="hidden"}
Looking for the **Nuxt** version?
::

## Setup

### Add to a Vue project

::steps{level="4"}

#### Install the Nuxt UI package

::code-group{sync="pm"}

```bash [pnpm]
pnpm add @nuxt/ui
```

```bash [yarn]
yarn add @nuxt/ui
```

```bash [npm]
npm install @nuxt/ui
```

```bash [bun]
bun add @nuxt/ui
```

::

::warning
If you're using **pnpm**, ensure that you either set [`shamefully-hoist=true`](https://pnpm.io/npmrc#shamefully-hoist) in your `.npmrc` file or install `tailwindcss`, `vue-router` and `@unhead/vue` in your project's root directory.
::

#### Add the Nuxt UI Vite plugin in your `vite.config.ts`{lang="ts-type"}

```ts [vite.config.ts]{3,8}
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import ui from '@nuxt/ui/vite'

export default defineConfig({
  plugins: [
    vue(),
    ui()
  ]
})
```

::tip
Nuxt UI registers `unplugin-auto-import` and `unplugin-vue-components`, which will generate `auto-imports.d.ts` and `components.d.ts` type declaration files. You will likely want to gitignore these, and add them to your `tsconfig`.

```json [tsconfig.app.json]
{
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts"]
}
```

```bash [.gitignore]
# Auto-generated type declarations
auto-imports.d.ts
components.d.ts
```

::

::tip
Internally, Nuxt UI relies on custom alias to resolve the theme types. If you're using TypeScript, you should add an alias to your `tsconfig` to enable auto-completion in your `vite.config.ts`.

```json [tsconfig.node.json]
{
  "compilerOptions": {
    "paths": {
      "#build/ui": [
        "./node_modules/@nuxt/ui/.nuxt/ui"
      ]
    }
  }
}
```
::

#### Use the Nuxt UI Vue plugin in your `main.ts`

```ts [main.ts]{3,14}
import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import ui from '@nuxt/ui/vue-plugin'
import App from './App.vue'

const app = createApp(App)

const router = createRouter({
  routes: [],
  history: createWebHistory()
})

app.use(router)
app.use(ui)

app.mount('#app')
```

::note{to="#inertia"}
If you're using [Inertia.js](https://inertiajs.com/), you can skip the `vue-router` setup as Inertia provides its own routing system.
::

#### Import Tailwind CSS and Nuxt UI in your CSS

```css [assets/main.css]
@import "tailwindcss";
@import "@nuxt/ui";
```

::tip
Import the CSS file in your `main.ts`.

```ts [main.ts]{1}
import './assets/main.css'

import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import ui from '@nuxt/ui/vue-plugin'
import App from './App.vue'

const app = createApp(App)

const router = createRouter({
  routes: [],
  history: createWebHistory()
})

app.use(router)
app.use(ui)

app.mount('#app')
```

::

::callout{icon="i-simple-icons-visualstudiocode"}
It's recommended to install the [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss) extension for VSCode and add the following settings:

```json [.vscode/settings.json]
{
  "files.associations": {
    "*.css": "tailwindcss"
  },
  "editor.quickSuggestions": {
    "strings": "on"
  },
  "tailwindCSS.classAttributes": ["class", "ui"],
  "tailwindCSS.experimental.classRegex": [
    ["ui:\\s*{([^)]*)\\s*}", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ]
}
```

::

#### Wrap your app with App component

```vue [App.vue]
<template>
  <UApp>
    <RouterView />
  </UApp>
</template>
```

::note{to="/components/app"}
The `App` component sets up global config and is required for **Toast**, **Tooltip** and **programmatic overlays**.
::

#### Add the `isolate` class to your root container

```html [index.html]{9}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Nuxt UI</title>
  </head>
  <body>
    <div id="app" class="isolate"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
```

::note
This ensures styles are scoped to your app and prevents issues with overlays and stacking contexts.
::

::

### Use our Vue starter

Start your project using the [nuxtlabs/nuxt-ui-vue-starter](https://github.com/nuxtlabs/nuxt-ui-vue-starter) template with Nuxt UI pre-configured.

Create a new project locally by running the following command:

```bash [Terminal]
npm create nuxt@latest -- -t github:nuxtlabs/nuxt-ui-vue-starter
```

::note
The `<my-app>` argument is the name of the directory where the project will be created, replace it with your project name.
::

Once the installation is complete, navigate into your project and start the development server:

```bash [Terminal]
cd <my-app>
npm run dev
```

## Options

You can customize Nuxt UI by providing options in your `vite.config.ts`.

### `prefix`

Use the `prefix` option to change the prefix of the components.

- Default: `U`{lang="ts-type"}

```ts [vite.config.ts]
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import ui from '@nuxt/ui/vite'

export default defineConfig({
  plugins: [
    vue(),
    ui({
      prefix: 'Nuxt'
    })
  ]
})
```

### `ui`

Use the `ui` option to provide configuration for Nuxt UI.

```ts [vite.config.ts]
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import ui from '@nuxt/ui/vite'

export default defineConfig({
  plugins: [
    vue(),
    ui({
      ui: {
        colors: {
          primary: 'green',
          neutral: 'slate'
        }
      }
    })
  ]
})
```

### `colorMode`

Use the `colorMode` option to enable or disable the color mode integration from `@vueuse/core`.

- Default: `true`{lang="ts-type"}

```ts [vite.config.ts]
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import ui from '@nuxt/ui/vite'

export default defineConfig({
  plugins: [
    vue(),
    ui({
      colorMode: false
    })
  ]
})
```

### `theme.colors`

Use the `theme.colors` option to define the dynamic color aliases used to generate components theme.

- Default: `['primary', 'secondary', 'success', 'info', 'warning', 'error']`{lang="ts-type" class="inline"}

```ts [vite.config.ts]
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import ui from '@nuxt/ui/vite'

export default defineConfig({
  plugins: [
    vue(),
    ui({
      theme: {
        colors: ['primary', 'error']
      }
    })
  ]
})
```

::tip{to="/getting-started/theme#colors"}
Learn more about color customization and theming in the Theme section.
::

### `theme.transitions`

Use the `theme.transitions` option to enable or disable transitions on components.

- Default: `true`{lang="ts-type"}

```ts [vite.config.ts]
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import ui from '@nuxt/ui/vite'

export default defineConfig({
  plugins: [
    vue(),
    ui({
      theme: {
        transitions: false
      }
    })
  ]
})
```

::note
This option adds the `transition-colors` class on components with hover or active states.
::

### `theme.defaultVariants` :badge{label="New" class="align-text-top"}

Use the `theme.defaultVariants` option to override the default `color` and `size` variants for components.

- Default: `{ color: 'primary', size: 'md' }`{lang="ts-type"}

```ts [vite.config.ts]
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import ui from '@nuxt/ui/vite'

export default defineConfig({
  plugins: [
    vue(),
    ui({
      theme: {
        defaultVariants: {
          color: 'neutral',
          size: 'sm'
        }
      }
    })
  ]
})
```

### `inertia`

Use the `inertia` option to enable compatibility with [Inertia.js](https://inertiajs.com/).

```ts [vite.config.ts]
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import ui from '@nuxt/ui/vite'

export default defineConfig({
  plugins: [
    vue(),
    ui({
      inertia: true
    })
  ]
})
```

::note
When using this option, `vue-router` is not required as Inertia.js provides its own routing system. The components that would normally use `RouterLink` will automatically use Inertia's `InertiaLink` component instead.
::

## Continuous Releases

Nuxt UI uses [pkg.pr.new](https://github.com/stackblitz-labs/pkg.pr.new) for continuous preview releases, providing developers with instant access to the latest features and bug fixes without waiting for official releases.

Automatic preview releases are created for all commits and PRs to the `v3` branch. Use them by replacing your package version with the specific commit hash or PR number.

```diff [package.json]
{
  "dependencies": {
-   "@nuxt/ui": "^3.0.0",
+   "@nuxt/ui": "https://pkg.pr.new/@nuxt/ui@4c96909",
  }
}
```

::note
**pkg.pr.new** will automatically comment on PRs with the installation URL, making it easy to test changes.
::
