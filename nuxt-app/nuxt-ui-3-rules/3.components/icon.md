---
description: A component to display any icon from Iconify.
category: element
links:
  - label: Ic<PERSON>s
    to: https://icones.js.org/
    target: _blank
    icon: i-custom-icones-js
---

## Usage

Use the `name` prop to display an icon:

::component-code
---
props:
  name: 'i-lucide-lightbulb'
  class: 'size-5'
---
::

::framework-only
#nuxt
:::caution{to="/getting-started/icons/nuxt#collections"}
It's highly recommended to install the icons collections you need, read more about this.
:::
::

## API

### Props

:component-props

## Changelog

:component-changelog
